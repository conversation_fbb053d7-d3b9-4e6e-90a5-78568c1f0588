# 服务目录结构说明

本目录包含了 wmplayer 项目的所有 Go 服务文件，按功能分类组织。

## 目录结构

```
services/
├── core/           # 核心业务服务
├── media/          # 媒体相关服务
├── system/         # 系统级服务
└── README.md       # 本说明文件
```

## 服务分类

### 🎯 核心业务服务 (core/)

这些服务处理应用的核心业务逻辑：

- **loginservice.go** - 用户登录服务
  - 手机验证码登录
  - 二维码登录
  - 微信登录
  - 用户状态管理

- **searchservice.go** - 搜索服务
  - 歌曲搜索
  - 专辑搜索
  - 歌手搜索
  - 歌单搜索
  - 热搜关键词

- **discoverservice.go** - 发现页面服务
  - 新歌速递
  - 新碟上架
  - 推荐歌曲
  - 音乐分类

- **albumservice.go** - 专辑服务
  - 专辑详情获取
  - 专辑歌曲列表
  - 歌单详情

- **playlistservice.go** - 播放列表服务
  - 播放列表管理
  - 播放模式控制
  - 随机播放
  - 循环播放

### 🎵 媒体相关服务 (media/)

这些服务处理音乐播放和媒体管理：

- **localmusicservice.go** - 本地音乐服务
  - 本地文件扫描
  - 元数据提取
  - 封面提取
  - 音乐库管理

- **playhistoryservice.go** - 播放历史服务
  - 播放记录管理
  - 历史统计
  - 播放次数统计

- **favoritesservice.go** - 收藏服务
  - 我喜欢的音乐
  - 收藏管理
  - 用户歌单

- **downloadservice.go** - 下载服务
  - 音乐下载管理
  - 下载记录
  - 文件管理

- **homepageservice.go** - 首页服务
  - 私人FM
  - 歌曲播放地址
  - 歌词搜索
  - AI推荐

### ⚙️ 系统级服务 (system/)

这些服务处理系统级功能和配置：

- **cacheservice.go** - 缓存服务
  - 音乐缓存
  - 封面缓存
  - HTTP服务器
  - OSD歌词功能

- **settingsservice.go** - 设置服务
  - 应用配置管理
  - 用户偏好设置
  - 主题设置
  - 快捷键配置

- **mediakeyservice.go** - 媒体键服务
  - 系统媒体键监听
  - 播放控制
  - MPRIS集成

- **mprisservice.go** - MPRIS服务
  - Linux媒体控制协议
  - D-Bus集成
  - 系统媒体控制

- **config.go** - 全局配置
  - API地址配置
  - 全局常量

- **cookiemanager.go** - Cookie管理
  - 用户认证状态
  - 登录信息管理
  - 会话管理

## 技术说明

### 包结构
所有服务文件都属于 `main` 包，这样可以：
- 共享类型定义（如 `ApiResponse`）
- 共享全局变量（如 `baseApi`、`GlobalCookieManager`）
- 简化依赖关系
- 保持 Wails3 的服务注册机制

### 符号链接
`services/` 目录中的文件都是指向根目录中实际文件的符号链接，这样：
- 保持了原有的包结构
- 提供了清晰的功能分类
- 便于开发者理解和维护
- 不影响构建过程

### 服务注册
在 `main.go` 中，所有服务都通过 Wails3 的 `application.NewService()` 方法注册：

```go
Services: []application.Service{
    application.NewService(&LoginService{}),
    application.NewService(homepageService),
    application.NewService(&SearchService{}),
    // ... 其他服务
},
```

## 开发指南

### 添加新服务
1. 在根目录创建新的服务文件（如 `newservice.go`）
2. 确保使用 `package main`
3. 在适当的 `services/` 子目录中创建符号链接
4. 在 `main.go` 中注册服务
5. 更新本README文档

### 修改现有服务
直接编辑根目录中的服务文件，符号链接会自动反映更改。

### 构建和运行
项目的构建和运行方式保持不变：
```bash
# 开发模式
wails3 dev

# 构建
task build
```

## 依赖关系

### 共享类型
- `ApiResponse[T]` - 通用API响应结构（定义在 loginservice.go）
- `CacheResponse` - 缓存服务响应结构（定义在 cacheservice.go）

### 共享变量
- `baseApi` - 后端API地址（定义在 config.go）
- `GlobalCookieManager` - 全局Cookie管理器（定义在 cookiemanager.go）
- `globalCacheService` - 全局缓存服务实例（定义在 main.go）

### 服务间依赖
- `HomepageService` 依赖 `CacheService`
- `MediaKeyService` 依赖 `MPRISService`
- 多个服务依赖 `GlobalCookieManager` 进行认证
